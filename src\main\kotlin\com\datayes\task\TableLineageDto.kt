package com.datayes.task

import com.datayes.lineage.ColumnLineageView
import com.datayes.lineage.TableLineageView
import com.datayes.metadata.MetadataDataSourceDto
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 表血缘关系 DTO (Table Lineage DTO)
 *
 * 包含表的上游和下游血缘关系信息以及表的基本信息
 */
data class TableLineageDto(
    val tableId: Long,
    val tableName: String,
    val schema: String?,
    val datasource: String,
    val metadata: List<MetadataDataSourceDto> = emptyList(),
    val columns: List<ColumnDto> = emptyList(),
    val upstream: List<TableRelationshipDto> = emptyList(),
    val downstream: List<TableRelationshipDto> = emptyList()
)

/**
 * 表关系 DTO (Table Relationship DTO)
 * 
 * 表示两个表之间的血缘关系
 */
data class TableRelationshipDto(
    val id: Long,
    val sourceTableId: Long,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceDatasource: String,
    val targetTableId: Long,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val lineageType: String?,
    val level: Int,
    val sourceSystem: String? = null,
    val columnMappings: List<ColumnMappingDto> = emptyList(),
    val sourceMetadata: List<MetadataDataSourceDto> = emptyList(),
    val targetMetadata: List<MetadataDataSourceDto> = emptyList(),
    val sourceSystemId: Long? = null,
    val sourceSystemName: String? = null,
    val sourceSystemAbbreviation: String? = null,
    val targetSystemId: Long? = null,
    val targetSystemName: String? = null,
    val targetSystemAbbreviation: String? = null,
    // 脚本血缘相关字段 (Script lineage related fields)
    val isFromScript: Boolean = false,
    val scriptId: Long? = null,
    val scriptName: String? = null,
    val temporaryLineageId: String? = null,
    // 前端展开按钮控制字段 (Frontend expand button control fields)
    val hasUpstream: Boolean = false,
    val hasDownstream: Boolean = false
)

/**
 * 列映射 DTO (Column Mapping DTO)
 *
 * 表示源表列和目标表列之间的映射关系
 */
data class ColumnMappingDto(
    val sourceColumn: String,
    val sourceDataType: String,
    val targetColumn: String,
    val targetDataType: String,
    val transformationType: String?,
    val transformationDescription: String?,
    val transformationExpression: String?,
    val confidenceScore: BigDecimal?,
    val sourceSystem: String? = null
)

/**
 * 列信息 DTO (Column DTO)
 *
 * 表示表中的列信息
 */
data class ColumnDto(
    val columnName: String,
    val dataType: String,
    val comment: String?,
    val isNullable: Boolean?,
    val isPrimaryKey: Boolean? = false,
    val defaultValue: String? = null,
    val columnPosition: Int? = null,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null
)

/**
 * 将 TableLineageView 列表转换为 TableRelationshipDto 列表
 */
fun List<TableLineageView>.toTableRelationshipDtos(): List<TableRelationshipDto> {
    return this.map { view ->
        TableRelationshipDto(
            id = view.relationshipId,
            sourceTableId = view.sourceTableId,
            sourceTable = view.sourceTable,
            sourceSchema = view.sourceSchema,
            sourceDatasource = view.sourceDatasource,
            targetTableId = view.targetTableId,
            targetTable = view.targetTable,
            targetSchema = view.targetSchema,
            targetDatasource = view.targetDatasource,
            lineageType = view.lineageType,
            level = view.level,
            sourceSystem = view.lineageSource,
            columnMappings = emptyList(), // 初始为空，后续填充
            sourceMetadata = emptyList(), // 初始为空，后续填充
            targetMetadata = emptyList(), // 初始为空，后续填充
            // 新增字段使用默认值，在后续处理中设置
            hasUpstream = false,
            hasDownstream = false
        )
    }
}

/**
 * 将 ColumnLineageView 转换为 ColumnMappingDto
 */
fun ColumnLineageView.toColumnMappingDto(): ColumnMappingDto {
    return ColumnMappingDto(
        sourceColumn = this.sourceColumn,
        sourceDataType = this.sourceDataType,
        targetColumn = this.targetColumn,
        targetDataType = this.targetDataType,
        transformationType = this.transformationType,
        transformationDescription = this.transformationDescription,
        transformationExpression = this.transformationExpression,
        confidenceScore = this.confidenceScore,
        sourceSystem = this.lineageSource
    )
}

/**
 * 将 TableMetadataDto 转换为 ColumnDto 列表
 */
fun List<com.datayes.metadata.TableColumnDto>.toColumnDtos(): List<ColumnDto> {
    return this.map { tableColumn ->
        ColumnDto(
            columnName = tableColumn.columnName,
            dataType = tableColumn.dataType,
            comment = tableColumn.comment,
            isNullable = tableColumn.isNullable,
            isPrimaryKey = tableColumn.isPrimaryKey,
            defaultValue = tableColumn.defaultValue,
            columnPosition = tableColumn.columnPosition,
            createdAt = tableColumn.createdAt,
            updatedAt = tableColumn.updatedAt
        )
    }
}

