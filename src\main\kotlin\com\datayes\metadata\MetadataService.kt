package com.datayes.metadata

import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Service
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * 元数据服务 (Metadata Service)
 * 
 * 基于 metadata_data_source 和 metadata_system_info 表提供元数据查询服务
 */
@Service
class MetadataService(private val jdbcTemplate: JdbcTemplate) {
    
    private val logger = LoggerFactory.getLogger(MetadataService::class.java)
    
    /**
     * 根据 lineage_datasources 表中的行ID查找匹配的元数据数据源
     * 匹配逻辑基于数据库类型、主机、端口和数据库名称
     */
    fun findMatchedMetadataDataSources(lineageDatasourceId: Long): MatchedMetadataResponse {
        logger.info("7a8b9c2d | 开始查找匹配的元数据数据源: lineageDatasourceId=$lineageDatasourceId")
        
        // 首先获取lineage_datasources的信息
        val lineageDatasource = getLineageDatasource(lineageDatasourceId)
            ?: throw IllegalArgumentException("未找到指定的血缘数据源: $lineageDatasourceId")
        
        // 查找匹配的metadata_data_source
        val matchedDataSources = findMatchedDataSourcesByLineageInfo(
            lineageDatasource.dbType,
            lineageDatasource.host,
            lineageDatasource.port,
            lineageDatasource.databaseName
        )
        
        logger.info("3e4f5a6b | 匹配完成: lineageDatasourceId=$lineageDatasourceId, 匹配数量=${matchedDataSources.size}")
        
        return MatchedMetadataResponse(
            lineageDatasourceId = lineageDatasourceId,
            matchedDataSources = matchedDataSources,
            totalCount = matchedDataSources.size
        )
    }
    
    /**
     * 获取所有有效的元数据数据源 (Get all active metadata data sources)
     */
    fun getAllActiveMetadataDataSources(): List<MetadataDataSourceDto> {
        logger.info("9c1d2e3f | 获取所有有效的元数据数据源")
        
        val sql = """
            SELECT 
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, 
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            WHERE mds.ACTIVE_FLAG = 1
            ORDER BY mds.SOURCE_NAME
        """.trimIndent()
        
        return jdbcTemplate.query(sql, metadataDataSourceRowMapper)
    }
    
    /**
     * 根据系统ID获取元数据数据源 (Get metadata data sources by system ID)
     */
    fun getMetadataDataSourcesBySystemId(systemId: Long): List<MetadataDataSourceDto> {
        logger.info("4g5h6i7j | 根据系统ID获取元数据数据源: systemId=$systemId")
        
        val sql = """
            SELECT 
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, 
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            WHERE mds.SYSTEM_ID = ? AND mds.ACTIVE_FLAG = 1
            ORDER BY mds.SOURCE_NAME
        """.trimIndent()
        
        return jdbcTemplate.query(sql, metadataDataSourceRowMapper, systemId)
    }
    
    /**
     * 获取所有有效的系统信息 (Get all active system info)
     */
    fun getAllActiveSystemInfo(): List<MetadataSystemInfoDto> {
        logger.info("8k9l0m1n | 获取所有有效的系统信息")
        
        val sql = """
            SELECT ID, SYSTEM_NAME, SYSTEM_ABBREVIATION, SYSTEM_TYPE, SYSTEM_MODULE,
                   MODULE_OWNER, DEVELOPMENT_DEPARTMENT, ACTIVE_FLAG, CREATE_BY, 
                   CREATE_TIME, UPDATE_BY, UPDATE_TIME
            FROM metadata_system_info
            WHERE ACTIVE_FLAG = 1
            ORDER BY SYSTEM_NAME
        """.trimIndent()
        
        return jdbcTemplate.query(sql, metadataSystemInfoRowMapper)
    }
    
    /**
     * 查询元数据数据源，支持分页和过滤 (Query metadata data sources with pagination and filtering)
     * 
     * @param systemId 系统ID，可选，为null时查询所有数据源
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选
     * @param schema 模式名称模糊匹配（从source_name提取），可选
     * @param page 页码，从1开始
     * @param size 每页大小
     * @return 分页后的元数据数据源信息
     */
    fun queryMetadataDataSourcesWithPagination(
        systemId: Long?,
        host: String?,
        databaseName: String?,
        schema: String?,
        page: Int = 1,
        size: Int = 20
    ): PagedMetadataDataSourcesDto {
        logger.info("ad4b96b8 | 查询元数据数据源: systemId=$systemId, host=$host, databaseName=$databaseName, schema=$schema, page=$page, size=$size")
        
        // 计算偏移量 (页码从1开始)
        val offset = (page - 1) * size
        
        val (whereClause, parameters) = buildDataSourceQueryConditions(systemId, host, databaseName, schema)
        
        // 查询总数
        val totalCountSql = """
            SELECT COUNT(DISTINCT mds.ID)
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            LEFT JOIN metadata_latest ml ON mds.ID = ml.DATA_SOURCE_ID
            $whereClause
        """.trimIndent()
        
        val totalCount = jdbcTemplate.queryForObject(
            totalCountSql,
            Int::class.java,
            *parameters.toTypedArray()
        ) ?: 0
        
        logger.debug("ab41f401 | 查询到元数据数据源总数: $totalCount")
        
        // 查询分页数据
        val dataSql = """
            SELECT DISTINCT
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY,
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION,
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME,
                CASE WHEN ml.DATA_SOURCE_ID IS NOT NULL THEN TRUE ELSE FALSE END as HAS_BEEN_COLLECTED
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            LEFT JOIN metadata_latest ml ON mds.ID = ml.DATA_SOURCE_ID
            $whereClause
            ORDER BY mds.SOURCE_NAME
            LIMIT ? OFFSET ?
        """.trimIndent()
        
        val queryParameters = parameters.toMutableList()
        queryParameters.add(size)
        queryParameters.add(offset)
        
        val dataSources = jdbcTemplate.query(
            dataSql,
            queryParameters.toTypedArray(),
            metadataDataSourceRowMapper
        )
        
        logger.info("y0z1a2b3 | 查询完成: 返回${dataSources.size}条记录，总数$totalCount")
        
        // 计算分页信息
        val totalPages = if (totalCount > 0) ((totalCount - 1) / size) + 1 else 0
        val hasNext = page < totalPages
        val hasPrevious = page > 1
        
        return PagedMetadataDataSourcesDto(
            content = dataSources,
            page = page,
            size = size,
            totalElements = totalCount,
            totalPages = totalPages,
            hasNext = hasNext,
            hasPrevious = hasPrevious,
            isFirst = page == 1,
            isLast = !hasNext,
            numberOfElements = dataSources.size
        )
    }

    /**
     * 查询血缘数据源，支持分页和过滤，并匹配元数据数据源 (Query lineage data sources with pagination and filtering, and match metadata data sources)
     *
     * @param systemId 系统ID，可选，通过匹配的元数据数据源过滤，为null时查询所有数据源
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选
     * @param datasourceName 数据源名称模糊匹配，可选
     * @param page 页码，从1开始
     * @param size 每页大小
     * @return 分页后的血缘数据源信息，包含匹配的元数据数据源
     */
    fun queryLineageDatasourcesWithPagination(
        systemId: Long?,
        host: String?,
        databaseName: String?,
        datasourceName: String?,
        page: Int = 1,
        size: Int = 20
    ): PagedLineageDatasourcesDto {
        logger.info("ld4b96b8 | 查询血缘数据源: systemId=$systemId, host=$host, databaseName=$databaseName, datasourceName=$datasourceName, page=$page, size=$size")

        // 如果指定了systemId，需要先查询所有血缘数据源，然后过滤出匹配指定系统的数据源
        if (systemId != null) {
            return queryLineageDatasourcesWithSystemIdFilter(systemId, host, databaseName, datasourceName, page, size)
        }

        // 计算偏移量 (页码从1开始)
        val offset = (page - 1) * size

        val (whereClause, parameters) = buildLineageDatasourceQueryConditions(host, databaseName, datasourceName)

        // 查询总数
        val countSql = """
            SELECT COUNT(DISTINCT lds.id)
            FROM lineage_datasources lds
            $whereClause
        """.trimIndent()

        val totalCount = jdbcTemplate.queryForObject(
            countSql,
            Int::class.java,
            *parameters.toTypedArray()
        ) ?: 0

        logger.debug("ly0z1a2b3 | 查询到血缘数据源总数: $totalCount")

        // 查询分页数据
        val dataSql = """
            SELECT DISTINCT
                lds.id, lds.datasource_name, lds.db_type, lds.host, lds.port,
                lds.database_name, lds.status, lds.connection_string,
                lds.created_at, lds.updated_at
            FROM lineage_datasources lds
            $whereClause
            ORDER BY lds.datasource_name
            LIMIT ? OFFSET ?
        """.trimIndent()

        val queryParameters = parameters.toMutableList()
        queryParameters.add(size)
        queryParameters.add(offset)

        val lineageDatasources = jdbcTemplate.query(
            dataSql,
            lineageDatasourceWithMetadataRowMapper,
            *queryParameters.toTypedArray()
        )

        logger.info("lf9g0h1i2 | 查询完成: 返回${lineageDatasources.size}条记录，总数$totalCount")

        // 计算分页信息
        val totalPages = if (totalCount > 0) ((totalCount - 1) / size) + 1 else 0
        val hasNext = page < totalPages
        val hasPrevious = page > 1

        return PagedLineageDatasourcesDto(
            content = lineageDatasources,
            page = page,
            size = size,
            totalElements = totalCount,
            totalPages = totalPages,
            hasNext = hasNext,
            hasPrevious = hasPrevious,
            isFirst = page == 1,
            isLast = !hasNext,
            numberOfElements = lineageDatasources.size
        )
    }

    /**
     * 查询血缘数据源并按系统ID过滤 (Query lineage datasources with system ID filter)
     *
     * 由于systemId来自匹配的metadata_data_source，需要先查询所有符合条件的血缘数据源，
     * 然后过滤出匹配指定系统的数据源
     */
    private fun queryLineageDatasourcesWithSystemIdFilter(
        systemId: Long,
        host: String?,
        databaseName: String?,
        datasourceName: String?,
        page: Int,
        size: Int
    ): PagedLineageDatasourcesDto {
        logger.info("lds4b96b8 | 查询血缘数据源(带系统ID过滤): systemId=$systemId, host=$host, databaseName=$databaseName, datasourceName=$datasourceName, page=$page, size=$size")

        val (whereClause, parameters) = buildLineageDatasourceQueryConditions(host, databaseName, datasourceName)

        // 查询所有符合基础条件的血缘数据源
        val allDataSql = """
            SELECT DISTINCT
                lds.id, lds.datasource_name, lds.db_type, lds.host, lds.port,
                lds.database_name, lds.status, lds.connection_string,
                lds.created_at, lds.updated_at
            FROM lineage_datasources lds
            $whereClause
            ORDER BY lds.datasource_name
        """.trimIndent()

        // 首先查询所有基础数据
        val rowMapper = RowMapper<Map<String, Any?>> { rs, _ ->
            mapOf(
                "id" to rs.getLong("id"),
                "datasource_name" to rs.getString("datasource_name"),
                "db_type" to rs.getString("db_type"),
                "host" to rs.getString("host"),
                "port" to rs.getInt("port"),
                "database_name" to rs.getString("database_name"),
                "status" to rs.getString("status"),
                "connection_string" to rs.getString("connection_string"),
                "created_at" to rs.getTimestamp("created_at"),
                "updated_at" to rs.getTimestamp("updated_at")
            )
        }

        val allLineageDatasourceData = jdbcTemplate.query(
            allDataSql,
            rowMapper,
            *parameters.toTypedArray()
        )

        // 然后过滤并构建最终结果
        val allLineageDatasources = allLineageDatasourceData.mapNotNull { data ->
            val lineageDatasourceId = data["id"] as Long
            val dbType = data["db_type"] as String
            val hostValue = data["host"] as String
            val port = data["port"] as Int
            val databaseNameValue = data["database_name"] as String

            // 查找匹配的元数据数据源
            val matchedMetadataDataSources = findMatchedDataSourcesByLineageInfo(dbType, hostValue, port, databaseNameValue)

            // 检查是否有匹配指定系统ID的元数据数据源
            val matchedMetadataDataSource = matchedMetadataDataSources.firstOrNull { it.systemId == systemId }

            if (matchedMetadataDataSource != null) {
                LineageDatasourceWithMetadataDto(
                    id = lineageDatasourceId,
                    datasourceName = data["datasource_name"] as String,
                    dbType = dbType,
                    host = hostValue,
                    port = port,
                    databaseName = databaseNameValue,
                    status = data["status"] as String,
                    systemId = matchedMetadataDataSource.systemId,
                    connectionString = data["connection_string"] as String,
                    createdAt = (data["created_at"] as? java.sql.Timestamp)?.toLocalDateTime(),
                    updatedAt = (data["updated_at"] as? java.sql.Timestamp)?.toLocalDateTime(),
                    matchedMetadataDataSource = matchedMetadataDataSource
                )
            } else {
                null
            }
        }

        logger.info("ldsf9g0h1i2 | 系统ID过滤完成: 匹配系统ID=$systemId 的数量=${allLineageDatasources.size}")

        // 手动分页
        val totalCount = allLineageDatasources.size
        val offset = (page - 1) * size
        val pagedContent = allLineageDatasources.drop(offset).take(size)

        // 计算分页信息
        val totalPages = if (totalCount > 0) ((totalCount - 1) / size) + 1 else 0
        val hasNext = page < totalPages
        val hasPrevious = page > 1

        return PagedLineageDatasourcesDto(
            content = pagedContent,
            page = page,
            size = size,
            totalElements = totalCount,
            totalPages = totalPages,
            hasNext = hasNext,
            hasPrevious = hasPrevious,
            isFirst = page == 1,
            isLast = !hasNext,
            numberOfElements = pagedContent.size
        )
    }
    
    /**
     * 根据数据源名称查找关联的系统信息 (Find system info by datasource name)
     * 
     * @param datasourceName 数据源名称
     * @return 系统信息，如果未找到则返回null
     */
    fun findSystemInfoByDatasourceName(datasourceName: String): MetadataSystemInfoDto? {
        logger.info("c4d5e6f7 | 根据数据源名称查找系统信息: datasourceName=$datasourceName")
        
        val sql = """
            SELECT msi.ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, msi.SYSTEM_TYPE, 
                   msi.SYSTEM_MODULE, msi.MODULE_OWNER, msi.DEVELOPMENT_DEPARTMENT, 
                   msi.ACTIVE_FLAG, msi.CREATE_BY, msi.CREATE_TIME, msi.UPDATE_BY, msi.UPDATE_TIME
            FROM metadata_system_info msi
            INNER JOIN metadata_data_source mds ON msi.ID = mds.SYSTEM_ID
            WHERE mds.SOURCE_NAME = ? AND msi.ACTIVE_FLAG = 1 AND mds.ACTIVE_FLAG = 1
            LIMIT 1
        """.trimIndent()
        
        val results = jdbcTemplate.query(sql, metadataSystemInfoRowMapper, datasourceName)
        val systemInfo = results.firstOrNull()
        
        logger.debug("g8h9i0j1 | 查找结果: datasourceName=$datasourceName, found=${systemInfo != null}")
        
        return systemInfo
    }

    /**
     * 获取系统间的血缘关系 (Get system-to-system lineage relationships)
     * 
     * 通过分析lineage_relationships中的表级关系，映射到系统级关系
     * 
     * @return 系统关系响应DTO
     */
    fun getSystemRelationships(): SystemRelationshipsResponseDto {
        logger.info("m5n6o7p8 | 开始获取系统间血缘关系")
        
        // 查询所有活跃的系统关系，通过lineage数据映射到metadata系统
        // 使用更灵活的匹配策略：优先精确匹配，其次模糊匹配
        val sql = """
            SELECT DISTINCT
                src_sys.system_id as source_system_id,
                tgt_sys.system_id as target_system_id,
                COUNT(DISTINCT lr.id) as relationship_count
            FROM lineage_relationships lr
            JOIN lineage_tables lt_src ON lr.source_table_id = lt_src.id
            JOIN lineage_tables lt_tgt ON lr.target_table_id = lt_tgt.id
            JOIN lineage_datasources lds_src ON lt_src.datasource_id = lds_src.id
            JOIN lineage_datasources lds_tgt ON lt_tgt.datasource_id = lds_tgt.id
            JOIN (
                SELECT DISTINCT 
                    lds.id as lineage_datasource_id,
                    msi.ID as system_id
                FROM lineage_datasources lds
                JOIN metadata_data_source mds ON (
                    -- 精确匹配：DB类型、主机、端口、数据库名
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND mds.DB_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.DB_PORT = lds.port 
                     AND mds.DB_NAME = lds.database_name)
                    OR
                    -- 模糊匹配：通过CUSTOM_JDBC_URL匹配主机和数据库
                    (mds.CUSTOM_JDBC_URL IS NOT NULL 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.database_name, '%'))
                    OR
                    -- 宽松匹配：只要DB类型相同且主机匹配
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND (mds.DB_URL LIKE CONCAT('%', lds.host, '%') OR mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%')))
                    OR
                    -- 超级宽松匹配：映射已知的别名
                    (lds.db_type = 'mysql' AND mds.DB_TYPE IN ('Mysql', 'MYSQL', 'mysql'))
                    OR
                    (lds.db_type = 'oracle' AND mds.DB_TYPE IN ('Oracle', 'ORACLE', 'oracle'))
                    OR
                    (lds.db_type = 'hive' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                    OR
                    (lds.db_type = 'hive2' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                )
                JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
                WHERE mds.ACTIVE_FLAG = 1 AND msi.ACTIVE_FLAG = 1
            ) src_sys ON lds_src.id = src_sys.lineage_datasource_id
            JOIN (
                SELECT DISTINCT 
                    lds.id as lineage_datasource_id,
                    msi.ID as system_id
                FROM lineage_datasources lds
                JOIN metadata_data_source mds ON (
                    -- 同样的匹配逻辑
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND mds.DB_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.DB_PORT = lds.port 
                     AND mds.DB_NAME = lds.database_name)
                    OR
                    (mds.CUSTOM_JDBC_URL IS NOT NULL 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.database_name, '%'))
                    OR
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND (mds.DB_URL LIKE CONCAT('%', lds.host, '%') OR mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%')))
                    OR
                    (lds.db_type = 'mysql' AND mds.DB_TYPE IN ('Mysql', 'MYSQL', 'mysql'))
                    OR
                    (lds.db_type = 'oracle' AND mds.DB_TYPE IN ('Oracle', 'ORACLE', 'oracle'))
                    OR
                    (lds.db_type = 'hive' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                    OR
                    (lds.db_type = 'hive2' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                )
                JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
                WHERE mds.ACTIVE_FLAG = 1 AND msi.ACTIVE_FLAG = 1
            ) tgt_sys ON lds_tgt.id = tgt_sys.lineage_datasource_id
            WHERE lr.is_active = 1 
            AND src_sys.system_id <> tgt_sys.system_id  -- 排除自引用
            GROUP BY src_sys.system_id, tgt_sys.system_id
            ORDER BY relationship_count DESC
        """.trimIndent()
        
        val systemRelationshipPairs = jdbcTemplate.query(sql) { rs, _ ->
            Triple(
                rs.getLong("source_system_id"),
                rs.getLong("target_system_id"),
                rs.getInt("relationship_count")
            )
        }
        
        logger.info("q9r0s1t2 | 查询到系统关系对数: ${systemRelationshipPairs.size}")
        
        // 获取所有涉及的系统信息
        val allSystemIds = systemRelationshipPairs.flatMap { listOf(it.first, it.second) }.distinct()
        val systemInfoMap = if (allSystemIds.isNotEmpty()) {
            getSystemInfoByIds(allSystemIds)
        } else {
            emptyMap()
        }
        
        // 检测循环引用
        val cyclicPairs = detectCyclicReferences(systemRelationshipPairs.map { it.first to it.second })
        
        // 构建结果
        val systemRelationships = systemRelationshipPairs.map { (sourceId, targetId, count) ->
            SystemRelationshipDto(
                sourceSystemId = sourceId,
                targetSystemId = targetId,
                isCyclicReference = cyclicPairs.contains(sourceId to targetId),
                relationshipCount = count,
                sourceSystemInfo = systemInfoMap[sourceId],
                targetSystemInfo = systemInfoMap[targetId]
            )
        }
        
        val metadata = SystemRelationshipMetadataDto(
            queryTimestamp = java.time.LocalDateTime.now().toString(),
            dataSource = "lineage_relationships",
            includeCyclicDetection = true,
            systemMappingMethod = "host_port_dbname_matching"
        )
        
        logger.info("u3v4w5x6 | 系统关系查询完成: 总关系数=${systemRelationships.size}, 涉及系统数=${allSystemIds.size}, 循环引用数=${cyclicPairs.size}")
        
        return SystemRelationshipsResponseDto(
            systemRelationships = systemRelationships,
            totalRelationships = systemRelationships.size,
            totalSystems = allSystemIds.size,
            cyclicReferencesCount = cyclicPairs.size,
            metadata = metadata
        )
    }

    /**
     * 根据系统ID列表获取系统信息 (Get system info by IDs)
     */
    private fun getSystemInfoByIds(systemIds: List<Long>): Map<Long, MetadataSystemInfoDto> {
        if (systemIds.isEmpty()) return emptyMap()
        
        val placeholders = systemIds.joinToString(",") { "?" }
        val sql = """
            SELECT ID, SYSTEM_NAME, SYSTEM_ABBREVIATION, SYSTEM_TYPE, SYSTEM_MODULE,
                   MODULE_OWNER, DEVELOPMENT_DEPARTMENT, ACTIVE_FLAG, CREATE_BY, 
                   CREATE_TIME, UPDATE_BY, UPDATE_TIME
            FROM metadata_system_info
            WHERE ID IN ($placeholders) AND ACTIVE_FLAG = 1
        """.trimIndent()
        
        val systemInfoList = jdbcTemplate.query(sql, metadataSystemInfoRowMapper, *systemIds.toTypedArray())
        return systemInfoList.associateBy { it.id }
    }

    /**
     * 检测循环引用 (Detect cyclic references)
     * 
     * 只检测直接的双向引用，不检测间接的循环
     */
    private fun detectCyclicReferences(relationships: List<Pair<Long, Long>>): Set<Pair<Long, Long>> {
        val cyclicPairs = mutableSetOf<Pair<Long, Long>>()
        val relationshipSet = relationships.toSet()
        
        for ((source, target) in relationships) {
            // 检查是否存在反向关系 (target -> source)
            if (relationshipSet.contains(target to source)) {
                cyclicPairs.add(source to target)
            }
        }
        
        return cyclicPairs
    }

    /**
     * 构建数据源查询条件 (Build data source query conditions)
     */
    private fun buildDataSourceQueryConditions(
        systemId: Long?,
        host: String?,
        databaseName: String?,
        schema: String?
    ): Pair<String, List<Any>> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()
        
        // 基础条件：只查询有效数据源
        conditions.add("mds.ACTIVE_FLAG = 1")
        
        // 系统ID过滤
        if (systemId != null) {
            conditions.add("mds.SYSTEM_ID = ?")
            parameters.add(systemId)
        }
        
        // host模糊匹配 (从DB_URL或CUSTOM_JDBC_URL中查找)
        if (!host.isNullOrBlank()) {
            conditions.add("(mds.DB_URL LIKE ? OR mds.CUSTOM_JDBC_URL LIKE ?)")
            val hostPattern = "%$host%"
            parameters.add(hostPattern)
            parameters.add(hostPattern)
        }
        
        // databaseName模糊匹配
        if (!databaseName.isNullOrBlank()) {
            conditions.add("mds.DB_NAME LIKE ?")
            parameters.add("%$databaseName%")
        }
        
        // schema模糊匹配 (从source_name中查找)
        if (!schema.isNullOrBlank()) {
            conditions.add("mds.SOURCE_NAME LIKE ?")
            parameters.add("%$schema%")
        }
        
        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }
        
        return Pair(whereClause, parameters)
    }

    /**
     * 构建血缘数据源查询条件 (Build lineage datasource query conditions)
     */
    private fun buildLineageDatasourceQueryConditions(
        host: String?,
        databaseName: String?,
        datasourceName: String?
    ): Pair<String, List<Any>> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        // 基础条件：只查询有效数据源
        conditions.add("lds.status = 'ACTIVE'")

        // host模糊匹配
        if (!host.isNullOrBlank()) {
            conditions.add("lds.host LIKE ?")
            parameters.add("%$host%")
        }

        // databaseName模糊匹配
        if (!databaseName.isNullOrBlank()) {
            conditions.add("lds.database_name LIKE ?")
            parameters.add("%$databaseName%")
        }

        // datasourceName模糊匹配
        if (!datasourceName.isNullOrBlank()) {
            conditions.add("lds.datasource_name LIKE ?")
            parameters.add("%$datasourceName%")
        }

        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }

        return Pair(whereClause, parameters)
    }

    /**
     * 获取lineage_datasources信息
     */
    private fun getLineageDatasource(id: Long): LineageDatasourceInfo? {
        val sql = """
            SELECT id, datasource_name, db_type, host, port, database_name, system_id, status
            FROM lineage_datasources
            WHERE id = ?
        """.trimIndent()
        
        val results = jdbcTemplate.query(sql, lineageDatasourceRowMapper, id)
        return results.firstOrNull()
    }
    
    /**
     * 标准化数据库类型，将 hive 和 hive2 统一为 hive
     */
    private fun normalizeDbType(dbType: String): String {
        return when (dbType.lowercase()) {
            "hive2" -> "hive"
            else -> dbType.lowercase()
        }
    }

    /**
     * 根据血缘数据源信息查找匹配的元数据数据源
     */
    private fun findMatchedDataSourcesByLineageInfo(
        dbType: String,
        host: String,
        port: Int,
        databaseName: String
    ): List<MetadataDataSourceDto> {
        val normalizedDbType = normalizeDbType(dbType)
        
        val sql = """
            SELECT DISTINCT
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, 
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME,
                CASE WHEN ml.DATA_SOURCE_ID IS NOT NULL THEN TRUE ELSE FALSE END as HAS_BEEN_COLLECTED
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            LEFT JOIN metadata_latest ml ON mds.ID = ml.DATA_SOURCE_ID
            WHERE mds.ACTIVE_FLAG = 1
            AND (
                (CASE WHEN LOWER(mds.DB_TYPE) = 'hive2' THEN 'hive' ELSE LOWER(mds.DB_TYPE) END = ? AND mds.DB_URL LIKE ? AND mds.DB_PORT = ? AND mds.DB_NAME = ?)
                OR (mds.CUSTOM_JDBC_URL IS NOT NULL AND mds.CUSTOM_JDBC_URL LIKE ? AND mds.CUSTOM_JDBC_URL LIKE ? AND mds.DB_NAME = ?)
                OR (CASE WHEN LOWER(mds.DB_TYPE) = 'hive2' THEN 'hive' ELSE LOWER(mds.DB_TYPE) END = ? AND (mds.DB_URL LIKE ? OR mds.CUSTOM_JDBC_URL LIKE ?) AND mds.DB_NAME = ?)
            )
            ORDER BY mds.SOURCE_NAME
        """.trimIndent()
        
        val hostPattern = "%$host%"
        val databasePattern = "%$databaseName%"
        
        // 记录实际执行的SQL语句（替换占位符为真实值）
        val actualSql = """
            SELECT DISTINCT
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, 
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME,
                CASE WHEN ml.DATA_SOURCE_ID IS NOT NULL THEN TRUE ELSE FALSE END as HAS_BEEN_COLLECTED
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            LEFT JOIN metadata_latest ml ON mds.ID = ml.DATA_SOURCE_ID
            WHERE mds.ACTIVE_FLAG = 1
            AND (
                (CASE WHEN LOWER(mds.DB_TYPE) = 'hive2' THEN 'hive' ELSE LOWER(mds.DB_TYPE) END = '$normalizedDbType' AND mds.DB_URL LIKE '$hostPattern' AND mds.DB_PORT = $port AND mds.DB_NAME = '$databaseName')
                OR (mds.CUSTOM_JDBC_URL IS NOT NULL AND mds.CUSTOM_JDBC_URL LIKE '$hostPattern' AND mds.CUSTOM_JDBC_URL LIKE '$databasePattern' AND mds.DB_NAME = '$databaseName')
                OR (CASE WHEN LOWER(mds.DB_TYPE) = 'hive2' THEN 'hive' ELSE LOWER(mds.DB_TYPE) END = '$normalizedDbType' AND (mds.DB_URL LIKE '$hostPattern' OR mds.CUSTOM_JDBC_URL LIKE '$hostPattern') AND mds.DB_NAME = '$databaseName')
            )
            ORDER BY mds.SOURCE_NAME
        """.trimIndent()
        
        logger.info("a8f3e7b2 | 执行数据源匹配查询: dbType=$dbType (normalized: $normalizedDbType), host=$host, port=$port, databaseName=$databaseName")
        logger.info("c5d9f1a4 | 实际执行SQL: $actualSql")
        
        return jdbcTemplate.query(
            sql, metadataDataSourceRowMapper,
            normalizedDbType, hostPattern, port, databaseName,
            hostPattern, databasePattern, databaseName,
            normalizedDbType, hostPattern, hostPattern, databaseName
        )
    }
    
    /**
     * MetadataDataSourceDto 行映射器
     */
    private val metadataDataSourceRowMapper = RowMapper<MetadataDataSourceDto> { rs, _ ->
        MetadataDataSourceDto(
            id = rs.getLong("ID"),
            sourceName = rs.getString("SOURCE_NAME"),
            dbType = rs.getString("DB_TYPE"),
            dbDriver = rs.getString("DB_DRIVER"),
            dbName = rs.getString("DB_NAME"),
            dbUrl = rs.getString("DB_URL"),
            dbPort = rs.getObject("DB_PORT") as? Int,
            dbUsername = rs.getString("DB_USERNAME"),
            customJdbcUrl = rs.getString("CUSTOM_JDBC_URL"),
            activeFlag = rs.getBoolean("ACTIVE_FLAG"),
            createBy = rs.getString("CREATE_BY"),
            createTime = rs.getTimestamp("CREATE_TIME")?.toLocalDateTime(),
            updateBy = rs.getString("UPDATE_BY"),
            updateTime = rs.getTimestamp("UPDATE_TIME")?.toLocalDateTime(),
            systemId = rs.getObject("SYSTEM_ID") as? Long,
            description = rs.getString("DESCRIPTION"),
            systemInfo = if (rs.getObject("SYS_ID") != null) {
                MetadataSystemInfoDto(
                    id = rs.getLong("SYS_ID"),
                    systemName = rs.getString("SYSTEM_NAME"),
                    systemAbbreviation = rs.getString("SYSTEM_ABBREVIATION"),
                    systemType = rs.getString("SYSTEM_TYPE"),
                    systemModule = rs.getString("SYSTEM_MODULE"),
                    moduleOwner = rs.getString("MODULE_OWNER"),
                    developmentDepartment = rs.getString("DEVELOPMENT_DEPARTMENT"),
                    activeFlag = rs.getBoolean("SYS_ACTIVE_FLAG"),
                    createBy = rs.getString("SYS_CREATE_BY"),
                    createTime = rs.getTimestamp("SYS_CREATE_TIME")?.toLocalDateTime(),
                    updateBy = rs.getString("SYS_UPDATE_BY"),
                    updateTime = rs.getTimestamp("SYS_UPDATE_TIME")?.toLocalDateTime()
                )
            } else null,
            hasBeenCollected = rs.getBoolean("HAS_BEEN_COLLECTED")
        )
    }
    
    /**
     * MetadataSystemInfoDto 行映射器
     */
    private val metadataSystemInfoRowMapper = RowMapper<MetadataSystemInfoDto> { rs, _ ->
        MetadataSystemInfoDto(
            id = rs.getLong("ID"),
            systemName = rs.getString("SYSTEM_NAME"),
            systemAbbreviation = rs.getString("SYSTEM_ABBREVIATION"),
            systemType = rs.getString("SYSTEM_TYPE"),
            systemModule = rs.getString("SYSTEM_MODULE"),
            moduleOwner = rs.getString("MODULE_OWNER"),
            developmentDepartment = rs.getString("DEVELOPMENT_DEPARTMENT"),
            activeFlag = rs.getBoolean("ACTIVE_FLAG"),
            createBy = rs.getString("CREATE_BY"),
            createTime = rs.getTimestamp("CREATE_TIME")?.toLocalDateTime(),
            updateBy = rs.getString("UPDATE_BY"),
            updateTime = rs.getTimestamp("UPDATE_TIME")?.toLocalDateTime()
        )
    }
    
    /**
     * LineageDatasource 行映射器
     */
    private val lineageDatasourceRowMapper = RowMapper<LineageDatasourceInfo> { rs, _ ->
        LineageDatasourceInfo(
            id = rs.getLong("id"),
            datasourceName = rs.getString("datasource_name"),
            dbType = rs.getString("db_type"),
            host = rs.getString("host"),
            port = rs.getInt("port"),
            databaseName = rs.getString("database_name"),
            systemId = rs.getObject("system_id") as? Long,
            status = rs.getString("status")
        )
    }

    /**
     * TableMetadataDto 行映射器 (Table Metadata Row Mapper)
     */
    private val tableMetadataRowMapper = RowMapper<TableMetadataDto> { rs, _ ->
        TableMetadataDto(
            id = rs.getLong("id"),
            tableName = rs.getString("table_name"),
            schemaName = rs.getString("schema_name"),
            tableType = rs.getString("table_type"),
            chineseName = rs.getString("chinese_name"),
            description = rs.getString("description"),
            syncFrequency = rs.getString("sync_frequency"),
            requirementId = rs.getString("requirement_id"),
            status = rs.getString("status"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime(),
            datasource = TableDatasourceDto(
                id = rs.getLong("datasource_id"),
                datasourceName = rs.getString("datasource_name"),
                dbType = rs.getString("db_type"),
                host = rs.getString("host"),
                port = rs.getInt("port"),
                databaseName = rs.getString("database_name"),
                connectionString = rs.getString("connection_string"),
                status = rs.getString("datasource_status")
            )
        )
    }

    /**
     * TableColumnDto 行映射器 (Table Column Row Mapper)
     */
    private val tableColumnRowMapper = RowMapper<TableColumnDto> { rs, _ ->
        TableColumnDto(
            id = rs.getLong("id"),
            columnName = rs.getString("column_name"),
            dataType = rs.getString("data_type"),
            comment = rs.getString("comment"),
            isNullable = rs.getObject("is_nullable") as? Boolean,
            isPrimaryKey = rs.getObject("is_primary_key") as? Boolean,
            defaultValue = rs.getString("default_value"),
            columnPosition = rs.getObject("column_position") as? Int,
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime()
        )
    }

    /**
     * LineageDatasourceWithMetadataDto 行映射器 (Row mapper for lineage datasource with metadata matching)
     */
    private val lineageDatasourceWithMetadataRowMapper = RowMapper<LineageDatasourceWithMetadataDto> { rs, _ ->
        val lineageDatasourceId = rs.getLong("id")
        val dbType = rs.getString("db_type")
        val host = rs.getString("host")
        val port = rs.getInt("port")
        val databaseName = rs.getString("database_name")

        // 查找匹配的元数据数据源
        val matchedMetadataDataSources = findMatchedDataSourcesByLineageInfo(dbType, host, port, databaseName)

        // 选择第一个匹配的元数据数据源，如果有多个则记录警告
        val matchedMetadataDataSource = when {
            matchedMetadataDataSources.isEmpty() -> {
                logger.debug("lm3n4o5p | 未找到匹配的元数据数据源: lineageDatasourceId=$lineageDatasourceId, dbType=$dbType, host=$host, port=$port, databaseName=$databaseName")
                null
            }
            matchedMetadataDataSources.size == 1 -> {
                logger.debug("lq6r7s8t | 找到唯一匹配的元数据数据源: lineageDatasourceId=$lineageDatasourceId, metadataDataSourceId=${matchedMetadataDataSources[0].id}")
                matchedMetadataDataSources[0]
            }
            else -> {
                logger.warn("lu9v0w1x | 找到多个匹配的元数据数据源，选择第一个: lineageDatasourceId=$lineageDatasourceId, 匹配数量=${matchedMetadataDataSources.size}, 选择的metadataDataSourceId=${matchedMetadataDataSources[0].id}")
                matchedMetadataDataSources[0]
            }
        }

        LineageDatasourceWithMetadataDto(
            id = lineageDatasourceId,
            datasourceName = rs.getString("datasource_name"),
            dbType = dbType,
            host = host,
            port = port,
            databaseName = databaseName,
            status = rs.getString("status"),
            systemId = matchedMetadataDataSource?.systemId, // 从匹配的元数据数据源获取systemId
            connectionString = rs.getString("connection_string"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime(),
            matchedMetadataDataSource = matchedMetadataDataSource
        )
    }

    /**
     * 根据表ID获取表的元数据信息 (Get table metadata by table ID)
     *
     * @param tableId 表ID
     * @return 表的元数据信息，如果未找到则返回null
     */
    fun getTableMetadataByTableId(tableId: Long): TableMetadataDto? {
        logger.info("t1a2b3c4 | 根据表ID获取表元数据: tableId=$tableId")

        val sql = """
            SELECT
                lt.id,
                lt.table_name,
                lt.schema_name,
                lt.table_type,
                lt.chinese_name,
                lt.description,
                lt.sync_frequency,
                lt.requirement_id,
                lt.status,
                lt.created_at,
                lt.updated_at,
                lds.id as datasource_id,
                lds.datasource_name,
                lds.db_type,
                lds.host,
                lds.port,
                lds.database_name,
                lds.connection_string,
                lds.status as datasource_status
            FROM lineage_tables lt
            JOIN lineage_datasources lds ON lt.datasource_id = lds.id
            WHERE lt.id = ? AND lt.status = 'ACTIVE' AND lds.status = 'ACTIVE'
        """.trimIndent()

        val results = jdbcTemplate.query(sql, tableMetadataRowMapper, tableId)
        val tableMetadata = results.firstOrNull()

        logger.debug("d5e6f7g8 | 查找表元数据结果: tableId=$tableId, found=${tableMetadata != null}")

        return tableMetadata
    }

    /**
     * 根据表ID获取表的列信息 (Get table columns by table ID)
     *
     * @param tableId 表ID
     * @return 表的列信息列表
     */
    fun getTableColumnsByTableId(tableId: Long): List<TableColumnDto> {
        logger.info("c1d2e3f4 | 根据表ID获取表列信息: tableId=$tableId")

        val sql = """
            SELECT
                lc.id,
                lc.column_name,
                lc.data_type,
                lc.comment,
                lc.is_nullable,
                lc.is_primary_key,
                lc.default_value,
                lc.column_position,
                lc.created_at,
                lc.updated_at
            FROM lineage_columns lc
            WHERE lc.table_id = ? AND lc.status = 'ACTIVE'
            ORDER BY lc.column_position ASC, lc.column_name ASC
        """.trimIndent()

        val columns = jdbcTemplate.query(sql, tableColumnRowMapper, tableId)

        logger.debug("g5h6i7j8 | 查找表列信息结果: tableId=$tableId, 列数量=${columns.size}")

        return columns
    }

    /**
     * 获取所有参与血缘关系的系统信息 (Get all systems that participate in lineage relationships)
     *
     * 通过分析lineage_relationships表，找出所有参与血缘关系的系统
     *
     * @return 参与血缘关系的系统信息列表
     */
    fun getSystemsInRelationships(): List<MetadataSystemInfoDto> {
        logger.info("x8y9z0a1 | 开始获取参与血缘关系的系统信息")
        
        val sql = """
            SELECT DISTINCT 
                msi.ID,
                msi.SYSTEM_NAME,
                msi.SYSTEM_ABBREVIATION
            FROM lineage_relationships lr
            JOIN lineage_tables lt_src ON lr.source_table_id = lt_src.id
            JOIN lineage_tables lt_tgt ON lr.target_table_id = lt_tgt.id
            JOIN lineage_datasources lds_src ON lt_src.datasource_id = lds_src.id
            JOIN lineage_datasources lds_tgt ON lt_tgt.datasource_id = lds_tgt.id
            JOIN (
                SELECT DISTINCT 
                    lds.id as lineage_datasource_id,
                    msi.ID as system_id
                FROM lineage_datasources lds
                JOIN metadata_data_source mds ON (
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND mds.DB_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.DB_PORT = lds.port 
                     AND mds.DB_NAME = lds.database_name)
                    OR
                    (mds.CUSTOM_JDBC_URL IS NOT NULL 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%') 
                     AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.database_name, '%'))
                    OR
                    (LOWER(lds.db_type) = LOWER(mds.DB_TYPE) 
                     AND (mds.DB_URL LIKE CONCAT('%', lds.host, '%') OR mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%')))
                    OR
                    (lds.db_type = 'mysql' AND mds.DB_TYPE IN ('Mysql', 'MYSQL', 'mysql'))
                    OR
                    (lds.db_type = 'oracle' AND mds.DB_TYPE IN ('Oracle', 'ORACLE', 'oracle'))
                    OR
                    (lds.db_type = 'hive' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                    OR
                    (lds.db_type = 'hive2' AND mds.DB_TYPE IN ('Hive', 'HIVE', 'hive'))
                )
                JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
                WHERE mds.ACTIVE_FLAG = 1 AND msi.ACTIVE_FLAG = 1
            ) system_mapping ON (lds_src.id = system_mapping.lineage_datasource_id OR lds_tgt.id = system_mapping.lineage_datasource_id)
            JOIN metadata_system_info msi ON msi.ID = system_mapping.system_id
            WHERE lr.is_active = 1 
            AND msi.ACTIVE_FLAG = 1
            ORDER BY msi.SYSTEM_NAME
        """.trimIndent()
        
        val systems = jdbcTemplate.query(sql) { rs, _ ->
            MetadataSystemInfoDto(
                id = rs.getLong("ID"),
                systemName = rs.getString("SYSTEM_NAME"),
                systemAbbreviation = rs.getString("SYSTEM_ABBREVIATION"),
                systemType = null,
                systemModule = null,
                moduleOwner = null,
                developmentDepartment = null,
                activeFlag = true,
                createBy = null,
                createTime = null,
                updateBy = null,
                updateTime = null
            )
        }
        
        logger.info("b2c3d4e5 | 获取参与血缘关系的系统信息完成: 系统数量=${systems.size}")
        
        return systems
    }
}

/**
 * 血缘数据源信息 (用于内部查询)
 */
private data class LineageDatasourceInfo(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val systemId: Long?,
    val status: String
)